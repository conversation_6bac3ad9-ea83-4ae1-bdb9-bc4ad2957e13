import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

import { DeveloperRepositoryDto } from './developer-repository.dto';
import { GiteaProfileDto } from './gitea-profile.dto';

describe('Developer DTOs - Large Files', () => {
  describe('DeveloperRepositoryDto', () => {
    it('should be defined', () => {
      expect(DeveloperRepositoryDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 456,
        name: 'test-repository',
        fullName: 'testuser/test-repository',
        description: 'A test repository',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 1024,
        starsCount: 5,
        forksCount: 2,
        watchersCount: 3,
        openIssuesCount: 1,
        defaultBranch: 'main',
        language: 'TypeScript',
        topics: ['nodejs', 'typescript', 'api'],
        htmlUrl: 'https://gitea.example.com/testuser/test-repository',
        cloneUrl: 'https://gitea.example.com/testuser/test-repository.git',
        sshUrl: '*********************:testuser/test-repository.git',
        isPublished: true,
        marketplaceItemId: 'item-789',
        hasMarketplaceMetadata: true,
        marketplaceMetadata: { category: 'tools', price: 9.99 },
        syncStatus: 'completed',
        lastSyncAt: new Date('2023-01-01T12:00:00Z'),
        giteaCreatedAt: new Date('2023-01-01T10:00:00Z'),
        giteaUpdatedAt: new Date('2023-01-01T11:00:00Z'),
        giteaPushedAt: new Date('2023-01-01T11:30:00Z'),
        createdAt: new Date('2023-01-01T10:00:00Z'),
        updatedAt: new Date('2023-01-01T12:00:00Z'),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required data', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-minimal',
        giteaRepoId: 123,
        name: 'minimal-repo',
        fullName: 'user/minimal-repo',
        visibility: 'private',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: true,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'master',
        topics: [],
        htmlUrl: 'https://gitea.example.com/user/minimal-repo',
        cloneUrl: 'https://gitea.example.com/user/minimal-repo.git',
        sshUrl: '*********************:user/minimal-repo.git',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require string fields', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 123, // Should be string
        name: 456, // Should be string
        fullName: 789, // Should be string
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        topics: [],
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFieldErrors = errors.filter(error => 
        ['id', 'name', 'fullName'].includes(error.property)
      );
      expect(stringFieldErrors.length).toBeGreaterThan(0);
    });

    it('should require number fields', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 'not-a-number', // Should be number
        name: 'test-repo',
        fullName: 'user/test-repo',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 'not-a-number', // Should be number
        starsCount: 'not-a-number', // Should be number
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        topics: [],
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const numberFieldErrors = errors.filter(error => 
        ['giteaRepoId', 'size', 'starsCount'].includes(error.property)
      );
      expect(numberFieldErrors.length).toBeGreaterThan(0);
    });

    it('should require boolean fields', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 456,
        name: 'test-repo',
        fullName: 'user/test-repo',
        visibility: 'public',
        isFork: 'not-a-boolean', // Should be boolean
        isTemplate: 'not-a-boolean', // Should be boolean
        isArchived: false,
        isEmpty: false,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        topics: [],
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const booleanFieldErrors = errors.filter(error => 
        ['isFork', 'isTemplate'].includes(error.property)
      );
      expect(booleanFieldErrors.length).toBeGreaterThan(0);
    });

    it('should validate visibility enum', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 456,
        name: 'test-repo',
        fullName: 'user/test-repo',
        visibility: 'invalid-visibility', // Should be 'public' or 'private'
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        topics: [],
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const visibilityError = errors.find(error => error.property === 'visibility');
      expect(visibilityError).toBeDefined();
    });

    it('should validate topics as array', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 456,
        name: 'test-repo',
        fullName: 'user/test-repo',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        topics: 'not-an-array', // Should be array
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const topicsError = errors.find(error => error.property === 'topics');
      expect(topicsError).toBeDefined();
    });

    it('should handle optional fields', async () => {
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 456,
        name: 'test-repo',
        fullName: 'user/test-repo',
        description: null, // Optional
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        language: null, // Optional
        topics: [],
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        marketplaceItemId: null, // Optional
        hasMarketplaceMetadata: false,
        marketplaceMetadata: undefined, // Optional
        syncStatus: 'pending',
        lastSyncAt: null, // Optional
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        giteaPushedAt: null, // Optional
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should create instance', () => {
      const dto = new DeveloperRepositoryDto();
      expect(dto).toBeInstanceOf(DeveloperRepositoryDto);
    });
  });
