import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

// Import developer DTOs with 0% coverage
import { DeveloperAnalytics } from './developer-analytics.dto';
import { DeveloperPayoutSettings } from './developer-payout-settings.dto';
import { DeveloperPayout } from './developer-payout.dto';
import { GiteaWebhookPayload } from './gitea-webhook-payload.dto';
import { MarketplaceMetadataDto } from './marketplace-metadata.dto';
import { PayoutRequest } from './payout-request.dto';
import { PayoutSettings } from './payout-settings.dto';
import { PublishRepositoryRequest } from './publish-repository-request.dto';
import { RepositoryAnalytics } from './repository-analytics.dto';
import { RolePayoutSettings } from './role-payout-settings.dto';
import { UpdateDeveloperPayoutSettingsRequest } from './update-developer-payout-settings-request.dto';
import { UpdatePayoutSettingsRequest } from './update-payout-settings-request.dto';
import { UpdateRolePayoutSettingsRequest } from './update-role-payout-settings-request.dto';

describe('Developer DTOs', () => {
  describe('DeveloperAnalytics', () => {
    it('should be defined', () => {
      expect(DeveloperAnalytics).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(DeveloperAnalytics, {
        period: '30days',
        totalRevenue: 1500.50,
        totalDownloads: 1000,
        totalViews: 5000,
        activeItems: 5,
        topPerformingItems: [{ id: '1', name: 'Item 1' }],
        revenueByPeriod: [{ period: 'Jan', revenue: 500 }],
        categoryBreakdown: { tools: 3, plugins: 2 },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object', async () => {
      const dto = plainToClass(DeveloperAnalytics, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(DeveloperAnalytics, {
        totalRevenue: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('totalRevenue');
    });
  });

  describe('DeveloperPayoutSettings', () => {
    it('should be defined', () => {
      expect(DeveloperPayoutSettings).toBeDefined();
    });

    it('should create instance', () => {
      const settings = new DeveloperPayoutSettings();
      expect(settings).toBeInstanceOf(DeveloperPayoutSettings);
    });
  });

  describe('DeveloperPayout', () => {
    it('should be defined', () => {
      expect(DeveloperPayout).toBeDefined();
    });

    it('should create instance', () => {
      const payout = new DeveloperPayout();
      expect(payout).toBeInstanceOf(DeveloperPayout);
    });
  });

  describe('GiteaWebhookPayload', () => {
    it('should be defined', () => {
      expect(GiteaWebhookPayload).toBeDefined();
    });

    it('should create instance', () => {
      const payload = new GiteaWebhookPayload();
      expect(payload).toBeInstanceOf(GiteaWebhookPayload);
    });
  });

  describe('MarketplaceMetadataDto', () => {
    it('should be defined', () => {
      expect(MarketplaceMetadataDto).toBeDefined();
    });

    it('should validate with valid complete data', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        name: 'My Awesome Plugin',
        description: 'A plugin that does amazing things',
        category: 'Development Tools',
        tags: ['productivity', 'development'],
        version: '1.0.0',
        author: 'John Doe',
        license: 'MIT',
        homepage: 'https://example.com',
        documentation: 'https://docs.example.com',
        screenshots: ['https://example.com/screenshot1.png'],
        pricing: {
          type: 'one_time',
          basePrice: 9.99,
          currency: 'USD',
        },
        requirements: {
          rsgliderVersion: '>=1.0.0',
          dependencies: ['nodejs', 'git'],
          platforms: ['windows', 'mac', 'linux'],
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        name: 'Simple Plugin',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate pricing enum', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        pricing: {
          type: 'invalid_type',
        },
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });

    it('should validate free pricing', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        pricing: {
          type: 'free',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate subscription pricing', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        pricing: {
          type: 'subscription',
          basePrice: 4.99,
          currency: 'EUR',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('PayoutRequest', () => {
    it('should be defined', () => {
      expect(PayoutRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new PayoutRequest();
      expect(request).toBeInstanceOf(PayoutRequest);
    });
  });

  describe('PayoutSettings', () => {
    it('should be defined', () => {
      expect(PayoutSettings).toBeDefined();
    });

    it('should create instance', () => {
      const settings = new PayoutSettings();
      expect(settings).toBeInstanceOf(PayoutSettings);
    });
  });

  describe('PublishRepositoryRequest', () => {
    it('should be defined', () => {
      expect(PublishRepositoryRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new PublishRepositoryRequest();
      expect(request).toBeInstanceOf(PublishRepositoryRequest);
    });
  });

  describe('RepositoryAnalytics', () => {
    it('should be defined', () => {
      expect(RepositoryAnalytics).toBeDefined();
    });

    it('should create instance', () => {
      const analytics = new RepositoryAnalytics();
      expect(analytics).toBeInstanceOf(RepositoryAnalytics);
    });
  });

  describe('RolePayoutSettings', () => {
    it('should be defined', () => {
      expect(RolePayoutSettings).toBeDefined();
    });

    it('should create instance', () => {
      const settings = new RolePayoutSettings();
      expect(settings).toBeInstanceOf(RolePayoutSettings);
    });
  });

  describe('UpdateDeveloperPayoutSettingsRequest', () => {
    it('should be defined', () => {
      expect(UpdateDeveloperPayoutSettingsRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new UpdateDeveloperPayoutSettingsRequest();
      expect(request).toBeInstanceOf(UpdateDeveloperPayoutSettingsRequest);
    });
  });

  describe('UpdatePayoutSettingsRequest', () => {
    it('should be defined', () => {
      expect(UpdatePayoutSettingsRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new UpdatePayoutSettingsRequest();
      expect(request).toBeInstanceOf(UpdatePayoutSettingsRequest);
    });
  });

  describe('UpdateRolePayoutSettingsRequest', () => {
    it('should be defined', () => {
      expect(UpdateRolePayoutSettingsRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new UpdateRolePayoutSettingsRequest();
      expect(request).toBeInstanceOf(UpdateRolePayoutSettingsRequest);
    });
  });
});
