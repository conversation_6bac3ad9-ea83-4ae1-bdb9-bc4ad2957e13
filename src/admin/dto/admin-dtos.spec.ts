import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

// Import admin DTOs with 0% coverage
import { AdminDeveloperView } from './admin-developer-view.dto';
import { AdminUpdateUserRequest } from './admin-update-user-request.dto';
import { AdminUserView } from './admin-user-view.dto';

describe('Admin DTOs', () => {
  describe('AdminDeveloperView', () => {
    it('should be defined', () => {
      expect(AdminDeveloperView).toBeDefined();
    });

    it('should create instance', () => {
      const view = new AdminDeveloperView();
      expect(view).toBeInstanceOf(AdminDeveloperView);
    });

    it('should validate empty object', async () => {
      const dto = new AdminDeveloperView();
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('AdminUserView', () => {
    it('should be defined', () => {
      expect(AdminUserView).toBeDefined();
    });

    it('should create instance', () => {
      const view = new AdminUserView();
      expect(view).toBeInstanceOf(AdminUserView);
    });

    it('should validate empty object', async () => {
      const dto = new AdminUserView();
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('AdminUpdateUserRequest', () => {
    it('should be defined', () => {
      expect(AdminUpdateUserRequest).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        emailVerified: true,
        twoFactorEnabled: false,
        notes: 'Test user account',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate email format', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        email: 'invalid-email',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should validate valid email format', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        email: '<EMAIL>',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        emailVerified: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('emailVerified');
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        firstName: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('firstName');
    });

    it('should validate partial updates', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        firstName: 'Jane',
        emailVerified: true,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate status field', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        status: 'suspended',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate notes field', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        notes: 'This is a test note for the user account.',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate twoFactorEnabled field', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        twoFactorEnabled: true,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });
});
