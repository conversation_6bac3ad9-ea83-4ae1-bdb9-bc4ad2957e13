import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

// Import user DTOs with 0% coverage
import { AppliedCoupon } from './applied-coupon.dto';
import { Pagination } from './pagination.dto';

describe('Users DTOs', () => {
  describe('AppliedCoupon', () => {
    it('should be defined', () => {
      expect(AppliedCoupon).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(AppliedCoupon, {
        code: 'SAVE20',
        name: '20% Off Coupon',
        discountAmount: 20,
        discountType: 'percentage',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object', async () => {
      const dto = plainToClass(AppliedCoupon, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate discountAmount as number', async () => {
      const dto = plainToClass(AppliedCoupon, {
        discountAmount: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('discountAmount');
    });
  });





  describe('Pagination', () => {
    it('should be defined', () => {
      expect(Pagination).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(Pagination, {
        page: 1,
        limit: 10,
        total: 100,
        totalPages: 10,
        hasNext: true,
        hasPrev: false,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(Pagination, {
        page: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('page');
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(Pagination, {
        hasNext: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('hasNext');
    });
  });


});
