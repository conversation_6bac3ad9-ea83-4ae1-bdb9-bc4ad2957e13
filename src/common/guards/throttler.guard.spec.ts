import { Reflector } from '@nestjs/core';
import { ThrottlerBehindProxyGuard } from './throttler.guard';

describe('ThrottlerBehindProxyGuard', () => {
  let guard: ThrottlerBehindProxyGuard;
  let reflector: Reflector;
  let mockStorage: any;

  beforeEach(() => {
    reflector = new Reflector();
    mockStorage = {
      get: jest.fn(),
      set: jest.fn(),
      increment: jest.fn(),
      decrement: jest.fn(),
      reset: jest.fn(),
    };

    guard = new ThrottlerBehindProxyGuard(
      {
        ttl: 60000,
        limit: 10,
      },
      mockStorage,
      reflector,
    );
  });

  describe('getTracker', () => {
    it('should return the first IP from ips array when available', async () => {
      const req = {
        ips: ['***********', '********'],
        ip: '127.0.0.1',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('***********');
    });

    it('should return the ip when ips array is empty', async () => {
      const req = {
        ips: [],
        ip: '127.0.0.1',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('127.0.0.1');
    });

    it('should return the ip when ips is not present', async () => {
      const req = {
        ip: '*************',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('*************');
    });

    it('should handle undefined ips gracefully', async () => {
      const req = {
        ips: undefined,
        ip: '********',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('********');
    });

    it('should handle null ips gracefully', async () => {
      const req = {
        ips: null,
        ip: '**********',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('**********');
    });
  });

  describe('guard instantiation', () => {
    it('should be defined', () => {
      expect(guard).toBeDefined();
    });

    it('should extend ThrottlerGuard', () => {
      expect(guard).toBeInstanceOf(ThrottlerBehindProxyGuard);
    });
  });
});
