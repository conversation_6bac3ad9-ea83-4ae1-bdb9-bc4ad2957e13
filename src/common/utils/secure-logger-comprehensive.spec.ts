import { Logger } from '@nestjs/common';
import { SecureLogger } from './secure-logger';

describe('SecureLogger - Comprehensive Coverage', () => {
  let logger: SecureLogger;
  let logSpy: jest.SpyInstance;
  let errorSpy: jest.SpyInstance;
  let warnSpy: jest.SpyInstance;
  let debugSpy: jest.SpyInstance;
  let verboseSpy: jest.SpyInstance;

  beforeEach(() => {
    logger = new SecureLogger();

    // Spy on the parent Logger methods (not the overridden ones)
    logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
    errorSpy = jest.spyOn(Logger.prototype, 'error').mockImplementation();
    warnSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    debugSpy = jest.spyOn(Logger.prototype, 'debug').mockImplementation();
    verboseSpy = jest.spyOn(Logger.prototype, 'verbose').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    delete process.env.NODE_ENV;
  });

  describe('log method', () => {
    it('should sanitize string messages', () => {
      const message = 'User password: secret123';
      logger.log(message, 'TestContext');

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('[REDACTED]'),
        'TestContext'
      );
    });

    it('should sanitize object messages', () => {
      const message = { password: 'secret123', username: 'test' };
      logger.log(message, 'TestContext');

      expect(logSpy).toHaveBeenCalledWith(
        expect.objectContaining({ password: '[REDACTED]', username: 'test' }),
        'TestContext'
      );
    });

    it('should handle log without context', () => {
      logger.log('test message');
      expect(logSpy).toHaveBeenCalledWith('test message');
    });
  });

  describe('error method', () => {
    it('should sanitize string error messages', () => {
      const message = 'Database error with token: abc123';
      logger.error(message, 'stack trace', 'ErrorContext');

      expect(errorSpy).toHaveBeenCalledWith(
        expect.stringContaining('[REDACTED]'),
        'stack trace',
        'ErrorContext'
      );
    });

    it('should sanitize object error messages', () => {
      const message = { error: 'failed', token: 'secret' };
      logger.error(message, 'stack trace', 'ErrorContext');

      expect(errorSpy).toHaveBeenCalledWith(
        expect.objectContaining({ error: 'failed', token: '[REDACTED]' }),
        'stack trace',
        'ErrorContext'
      );
    });

    it('should handle production database error traces', () => {
      process.env.NODE_ENV = 'production';
      const trace = 'Error: duplicate key value violates unique constraint "users_email_key"';

      logger.error('Database error', trace, 'ErrorContext');

      expect(errorSpy).toHaveBeenCalledWith(
        'Database error',
        undefined, // trace should be sanitized in production
        'ErrorContext'
      );
    });

    it('should preserve traces in non-production environments', () => {
      process.env.NODE_ENV = 'development';
      const trace = 'Error: duplicate key value violates unique constraint "users_email_key"';

      logger.error('Database error', trace, 'ErrorContext');

      expect(errorSpy).toHaveBeenCalledWith(
        'Database error',
        trace, // trace should be preserved in development
        'ErrorContext'
      );
    });

    it('should handle error without trace', () => {
      logger.error('test error', undefined, 'ErrorContext');
      expect(errorSpy).toHaveBeenCalledWith('test error', undefined, 'ErrorContext');
    });

    it('should handle error without context', () => {
      logger.error('test error', 'trace');
      expect(errorSpy).toHaveBeenCalledWith('test error', 'trace');
    });
  });

  describe('warn method', () => {
    it('should sanitize string warning messages', () => {
      const message = 'Warning: API key exposed: sk_test_123';
      logger.warn(message, 'WarnContext');

      expect(warnSpy).toHaveBeenCalledWith(
        expect.stringContaining('[REDACTED]'),
        'WarnContext'
      );
    });

    it('should sanitize object warning messages', () => {
      const message = { warning: 'rate limit', apiKey: 'secret' };
      logger.warn(message, 'WarnContext');

      expect(warnSpy).toHaveBeenCalledWith(
        expect.objectContaining({ warning: 'rate limit', apiKey: '[REDACTED]' }),
        'WarnContext'
      );
    });

    it('should handle warn without context', () => {
      logger.warn('test warning');
      expect(warnSpy).toHaveBeenCalledWith('test warning');
    });
  });

  describe('debug method', () => {
    it('should log debug messages in development', () => {
      process.env.NODE_ENV = 'development';

      logger.debug('Debug message', 'DebugContext');

      expect(debugSpy).toHaveBeenCalledWith('Debug message', 'DebugContext');
    });

    it('should sanitize object debug messages in development', () => {
      process.env.NODE_ENV = 'development';
      const message = { debug: 'info', password: 'secret' };

      logger.debug(message, 'DebugContext');

      expect(debugSpy).toHaveBeenCalledWith(
        expect.objectContaining({ debug: 'info', password: '[REDACTED]' }),
        'DebugContext'
      );
    });

    it('should not log debug messages in production', () => {
      process.env.NODE_ENV = 'production';

      logger.debug('Debug message', 'DebugContext');

      expect(debugSpy).not.toHaveBeenCalled();
    });

    it('should not log debug messages in test environment', () => {
      process.env.NODE_ENV = 'test';

      logger.debug('Debug message', 'DebugContext');

      expect(debugSpy).not.toHaveBeenCalled();
    });

    it('should not log debug messages when NODE_ENV is undefined', () => {
      delete process.env.NODE_ENV;

      logger.debug('Debug message', 'DebugContext');

      expect(debugSpy).not.toHaveBeenCalled();
    });
  });

  describe('verbose method', () => {
    it('should log verbose messages in development', () => {
      process.env.NODE_ENV = 'development';

      logger.verbose('Verbose message', 'VerboseContext');

      expect(verboseSpy).toHaveBeenCalledWith('Verbose message', 'VerboseContext');
    });

    it('should sanitize object verbose messages in development', () => {
      process.env.NODE_ENV = 'development';
      const message = { verbose: 'info', secret: 'hidden' };

      logger.verbose(message, 'VerboseContext');

      expect(verboseSpy).toHaveBeenCalledWith(
        expect.objectContaining({ verbose: 'info', secret: '[REDACTED]' }),
        'VerboseContext'
      );
    });

    it('should not log verbose messages in production', () => {
      process.env.NODE_ENV = 'production';

      logger.verbose('Verbose message', 'VerboseContext');

      expect(verboseSpy).not.toHaveBeenCalled();
    });

    it('should not log verbose messages in test environment', () => {
      process.env.NODE_ENV = 'test';

      logger.verbose('Verbose message', 'VerboseContext');

      expect(verboseSpy).not.toHaveBeenCalled();
    });

    it('should not log verbose messages when NODE_ENV is undefined', () => {
      delete process.env.NODE_ENV;

      logger.verbose('Verbose message', 'VerboseContext');

      expect(verboseSpy).not.toHaveBeenCalled();
    });
  });

  describe('logDatabaseOperation method', () => {
    it('should log successful database operations', () => {
      logger.logDatabaseOperation('INSERT', 'users', true);

      expect(logSpy).toHaveBeenCalledWith('Database INSERT on users completed successfully');
    });

    it('should log failed database operations in production', () => {
      process.env.NODE_ENV = 'production';
      const error = { message: 'Connection failed' };

      logger.logDatabaseOperation('SELECT', 'users', false, error);

      expect(errorSpy).toHaveBeenCalledWith('Database SELECT on users failed');
    });

    it('should log failed database operations with error details in development', () => {
      process.env.NODE_ENV = 'development';
      const error = { message: 'Connection failed' };

      logger.logDatabaseOperation('UPDATE', 'users', false, error);

      expect(errorSpy).toHaveBeenCalledWith(
        'Database UPDATE on users failed',
        'Connection failed'
      );
    });

    it('should handle database operation failure without error object', () => {
      logger.logDatabaseOperation('DELETE', 'users', false);

      expect(errorSpy).toHaveBeenCalledWith('Database DELETE on users failed', undefined);
    });
  });

  describe('logAuthEvent method', () => {
    it('should log successful auth events', () => {
      logger.logAuthEvent('login', 'user123456789', true, { ip: '127.0.0.1' });

      expect(logSpy).toHaveBeenCalledWith(
        'Auth event: login',
        expect.stringContaining('user-user1234***')
      );
    });

    it('should log failed auth events', () => {
      logger.logAuthEvent('login', 'user123456789', false, { reason: 'invalid password' });

      expect(warnSpy).toHaveBeenCalledWith(
        'Auth event failed: login',
        expect.stringContaining('user-user1234***')
      );
    });

    it('should handle auth events without userId', () => {
      logger.logAuthEvent('register', undefined, true);

      expect(logSpy).toHaveBeenCalledWith(
        'Auth event: register',
        expect.stringContaining('anonymous')
      );
    });

    it('should sanitize auth event details', () => {
      logger.logAuthEvent('login', 'user123', true, { password: 'secret', ip: '127.0.0.1' });

      const logCall = logSpy.mock.calls[0];
      expect(logCall[1]).toContain('[REDACTED]');
      expect(logCall[1]).toContain('127.0.0.1');
    });

    it('should default success to true when not provided', () => {
      logger.logAuthEvent('logout', 'user123');

      expect(logSpy).toHaveBeenCalledWith(
        'Auth event: logout',
        expect.any(String)
      );
    });
  });

  describe('logApiRequest method', () => {
    it('should log API requests with all parameters', () => {
      logger.logApiRequest('GET', '/api/users?token=secret', 200, 'user123456789', 150);

      expect(logSpy).toHaveBeenCalledWith(
        'API GET /api/users?token=secret - 200',
        expect.stringContaining('user-user1234***')
      );
    });

    it('should handle API requests without userId', () => {
      logger.logApiRequest('POST', '/api/auth/login', 401);

      expect(logSpy).toHaveBeenCalledWith(
        'API POST /api/auth/login - 401',
        expect.stringContaining('anonymous')
      );
    });

    it('should handle API requests without duration', () => {
      logger.logApiRequest('PUT', '/api/users/123', 200, 'user123');

      const logCall = logSpy.mock.calls[0];
      expect(logCall[1]).not.toContain('ms');
    });

    it('should sanitize URLs in API requests', () => {
      logger.logApiRequest('GET', '/api/data?password=secret&id=123', 200);

      const logCall = logSpy.mock.calls[0];
      expect(logCall[1]).toContain('%5BREDACTED%5D'); // URL encoded [REDACTED]
      expect(logCall[1]).toContain('id=123');
    });
  });
});
