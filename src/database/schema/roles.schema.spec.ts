import { roles } from './roles.schema';

describe('Roles Schema', () => {
  it('should be defined', () => {
    expect(roles).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(roles._.name).toBe('roles');
  });

  it('should have all required columns', () => {
    const columns = Object.keys(roles._.columns);
    expect(columns).toContain('id');
    expect(columns).toContain('name');
    expect(columns).toContain('description');
    expect(columns).toContain('isSystemRole');
    expect(columns).toContain('createdAt');
    expect(columns).toContain('updatedAt');
  });

  it('should have id as primary key', () => {
    expect(roles.id.primary).toBe(true);
  });

  it('should have name as not null and unique', () => {
    expect(roles.name.notNull).toBe(true);
    expect(roles.name.unique).toBe(true);
  });

  it('should have isSystemRole as not null with default false', () => {
    expect(roles.isSystemRole.notNull).toBe(true);
    expect(roles.isSystemRole.default).toBe(false);
  });

  it('should have createdAt and updatedAt as not null', () => {
    expect(roles.createdAt.notNull).toBe(true);
    expect(roles.updatedAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(roles.id.dataType).toBe('uuid');
    expect(roles.name.dataType).toBe('varchar');
    expect(roles.description.dataType).toBe('varchar');
    expect(roles.isSystemRole.dataType).toBe('boolean');
    expect(roles.createdAt.dataType).toBe('timestamp');
    expect(roles.updatedAt.dataType).toBe('timestamp');
  });

  it('should have correct varchar lengths', () => {
    expect(roles.name.length).toBe(50);
    expect(roles.description.length).toBe(255);
  });
});
