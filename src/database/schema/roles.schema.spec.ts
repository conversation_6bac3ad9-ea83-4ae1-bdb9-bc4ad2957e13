import { roles } from './roles.schema';

describe('Roles Schema', () => {
  it('should be defined', () => {
    expect(roles).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(roles[Symbol.for('drizzle:Name')]).toBe('roles');
  });

  it('should have all required columns', () => {
    expect(roles.id).toBeDefined();
    expect(roles.name).toBeDefined();
    expect(roles.description).toBeDefined();
    expect(roles.isSystemRole).toBeDefined();
    expect(roles.createdAt).toBeDefined();
    expect(roles.updatedAt).toBeDefined();
  });

  it('should have id as primary key', () => {
    expect(roles.id.primary).toBe(true);
  });

  it('should have name as not null and unique', () => {
    expect(roles.name.notNull).toBe(true);
    expect(roles.name.unique).toBe(true);
  });

  it('should have isSystemRole as not null with default false', () => {
    expect(roles.isSystemRole.notNull).toBe(true);
    expect(roles.isSystemRole.default).toBe(false);
  });

  it('should have createdAt and updatedAt as not null', () => {
    expect(roles.createdAt.notNull).toBe(true);
    expect(roles.updatedAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(roles.id.dataType).toBe('string');
    expect(roles.name.dataType).toBe('string');
    expect(roles.description.dataType).toBe('string');
    expect(roles.isSystemRole.dataType).toBe('boolean');
    expect(roles.createdAt.dataType).toBe('date');
    expect(roles.updatedAt.dataType).toBe('date');
  });

  it('should have correct varchar lengths', () => {
    expect(roles.name.size).toBe(50);
    expect(roles.description.size).toBe(255);
  });
});
