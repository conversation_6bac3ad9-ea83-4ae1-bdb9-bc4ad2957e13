/**
 * Marketplace Items Schema Tests
 * Tests the marketplace items schema definition, structure, and relations
 */

import { marketplaceItems, marketplaceItemsRelations } from './marketplace-items.schema';

describe('Marketplace Items Schema', () => {
  it('should be defined', () => {
    expect(marketplaceItems).toBeDefined();
    expect(marketplaceItemsRelations).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(marketplaceItems[Symbol.for('drizzle:Name')]).toBe('marketplace_items');
  });

  it('should have all required columns', () => {
    expect(marketplaceItems.id).toBeDefined();
    expect(marketplaceItems.developerId).toBeDefined();
    expect(marketplaceItems.giteaRepositoryId).toBeDefined();
    expect(marketplaceItems.name).toBeDefined();
    expect(marketplaceItems.slug).toBeDefined();
    expect(marketplaceItems.description).toBeDefined();
    expect(marketplaceItems.category).toBeDefined();
    expect(marketplaceItems.pricingType).toBeDefined();
    expect(marketplaceItems.status).toBeDefined();
    expect(marketplaceItems.createdAt).toBeDefined();
    expect(marketplaceItems.updatedAt).toBeDefined();
  });

  describe('foreign key references (lines 73-74, 140)', () => {
    it('should have foreign key references defined in schema', () => {
      // Test that the schema imports and uses the referenced tables
      // This ensures the foreign key references are properly defined
      expect(() => {
        // Import the referenced schemas to ensure they're accessible
        const { users } = require('./users.schema');
        const { giteaRepositories } = require('./gitea-repositories.schema');
        
        expect(users).toBeDefined();
        expect(giteaRepositories).toBeDefined();
        
        // Test that the schema can be used (this exercises the reference functions)
        expect(marketplaceItems.developerId.dataType).toBe('string');
        expect(marketplaceItems.giteaRepositoryId.dataType).toBe('string');
        expect(marketplaceItems.reviewedBy.dataType).toBe('string');
      }).not.toThrow();
    });
  });

  describe('JSON type definitions (lines 84, 105, 114-120, 135)', () => {
    it('should have JSON columns with proper type definitions', () => {
      // Test tags JSON column (line 84)
      expect(marketplaceItems.tags).toBeDefined();
      expect(marketplaceItems.tags.dataType).toBe('json');
      
      // Test screenshots JSON column (line 105)
      expect(marketplaceItems.screenshots).toBeDefined();
      expect(marketplaceItems.screenshots.dataType).toBe('json');
      
      // Test requirements JSON column (lines 114-120)
      expect(marketplaceItems.requirements).toBeDefined();
      expect(marketplaceItems.requirements.dataType).toBe('json');
      
      // Test metaKeywords JSON column (line 135)
      expect(marketplaceItems.metaKeywords).toBeDefined();
      expect(marketplaceItems.metaKeywords.dataType).toBe('json');
    });
  });

  describe('enum columns (lines 83, 87, 92)', () => {
    it('should have enum columns defined', () => {
      // Test category enum (line 83)
      expect(marketplaceItems.category).toBeDefined();
      expect(marketplaceItems.category.enumValues).toBeDefined();
      
      // Test pricingType enum (line 87)
      expect(marketplaceItems.pricingType).toBeDefined();
      expect(marketplaceItems.pricingType.enumValues).toBeDefined();
      
      // Test status enum (line 92)
      expect(marketplaceItems.status).toBeDefined();
      expect(marketplaceItems.status.enumValues).toBeDefined();
    });
  });

  describe('decimal columns with precision (lines 88, 125, 129)', () => {
    it('should have decimal columns with proper precision', () => {
      // Test basePrice decimal (line 88)
      expect(marketplaceItems.basePrice).toBeDefined();
      expect(marketplaceItems.basePrice.dataType).toBe('string'); // Drizzle represents decimals as strings
      
      // Test ratingAverage decimal (line 125)
      expect(marketplaceItems.ratingAverage).toBeDefined();
      expect(marketplaceItems.ratingAverage.dataType).toBe('string');
      
      // Test totalRevenue decimal (line 129)
      expect(marketplaceItems.totalRevenue).toBeDefined();
      expect(marketplaceItems.totalRevenue.dataType).toBe('string');
    });
  });

  describe('default values (lines 89, 92-95, 123-126, 129-130)', () => {
    it('should have correct default values', () => {
      // Test currency default (line 89)
      expect(marketplaceItems.currency.hasDefault).toBe(true);
      expect(marketplaceItems.currency.default).toBe('USD');
      
      // Test status default (line 92)
      expect(marketplaceItems.status.hasDefault).toBe(true);
      expect(marketplaceItems.status.default).toBe('draft');
      
      // Test visibility default (line 93)
      expect(marketplaceItems.visibility.hasDefault).toBe(true);
      expect(marketplaceItems.visibility.default).toBe('public');
      
      // Test boolean defaults (lines 94-95)
      expect(marketplaceItems.isFeatured.hasDefault).toBe(true);
      expect(marketplaceItems.isFeatured.default).toBe(false);
      expect(marketplaceItems.isActive.hasDefault).toBe(true);
      expect(marketplaceItems.isActive.default).toBe(true);
      
      // Test integer defaults (lines 123-126, 129-130)
      expect(marketplaceItems.downloadCount.hasDefault).toBe(true);
      expect(marketplaceItems.downloadCount.default).toBe(0);
      expect(marketplaceItems.viewCount.hasDefault).toBe(true);
      expect(marketplaceItems.viewCount.default).toBe(0);
      expect(marketplaceItems.ratingCount.hasDefault).toBe(true);
      expect(marketplaceItems.ratingCount.default).toBe(0);
      expect(marketplaceItems.totalSales.hasDefault).toBe(true);
      expect(marketplaceItems.totalSales.default).toBe(0);
    });
  });

  describe('onUpdate function (line 149)', () => {
    it('should have updatedAt with onUpdate function', () => {
      // Test that updatedAt has an onUpdate function (line 149)
      expect(marketplaceItems.updatedAt.onUpdateFn).toBeDefined();
      
      // Test that the onUpdate function returns a Date
      if (marketplaceItems.updatedAt.onUpdateFn) {
        const updateValue = marketplaceItems.updatedAt.onUpdateFn();
        expect(updateValue).toBeInstanceOf(Date);
      }
    });
  });

  describe('indexes (lines 150-161)', () => {
    it('should have all required indexes defined', () => {
      // The indexes are defined in the table configuration
      // We can test that the table has the expected structure
      expect(marketplaceItems._.config).toBeDefined();
      
      // Test that key columns exist for indexing
      expect(marketplaceItems.developerId).toBeDefined();
      expect(marketplaceItems.giteaRepositoryId).toBeDefined();
      expect(marketplaceItems.slug).toBeDefined();
      expect(marketplaceItems.category).toBeDefined();
      expect(marketplaceItems.status).toBeDefined();
      expect(marketplaceItems.visibility).toBeDefined();
      expect(marketplaceItems.isFeatured).toBeDefined();
      expect(marketplaceItems.isActive).toBeDefined();
      expect(marketplaceItems.pricingType).toBeDefined();
      expect(marketplaceItems.publishedAt).toBeDefined();
    });
  });

  describe('relations (lines 164-177)', () => {
    it('should have developer relation defined', () => {
      expect(marketplaceItemsRelations.developer).toBeDefined();
      expect(marketplaceItemsRelations.developer.relationName).toBe('developer');
    });

    it('should have giteaRepository relation defined', () => {
      expect(marketplaceItemsRelations.giteaRepository).toBeDefined();
      expect(marketplaceItemsRelations.giteaRepository.relationName).toBe('giteaRepository');
    });

    it('should have reviewer relation defined', () => {
      expect(marketplaceItemsRelations.reviewer).toBeDefined();
      expect(marketplaceItemsRelations.reviewer.relationName).toBe('reviewer');
    });
  });

  describe('type exports (lines 179-180)', () => {
    it('should export TypeScript types', () => {
      // Test that the types can be imported and used
      expect(() => {
        const { MarketplaceItem, NewMarketplaceItem } = require('./marketplace-items.schema');
        expect(MarketplaceItem).toBeDefined();
        expect(NewMarketplaceItem).toBeDefined();
      }).not.toThrow();
    });
  });

  describe('column constraints and properties', () => {
    it('should have correct not null constraints', () => {
      expect(marketplaceItems.developerId.notNull).toBe(true);
      expect(marketplaceItems.name.notNull).toBe(true);
      expect(marketplaceItems.slug.notNull).toBe(true);
      expect(marketplaceItems.description.notNull).toBe(true);
      expect(marketplaceItems.category.notNull).toBe(true);
      expect(marketplaceItems.pricingType.notNull).toBe(true);
      expect(marketplaceItems.status.notNull).toBe(true);
      expect(marketplaceItems.visibility.notNull).toBe(true);
      expect(marketplaceItems.isFeatured.notNull).toBe(true);
      expect(marketplaceItems.isActive.notNull).toBe(true);
      expect(marketplaceItems.createdAt.notNull).toBe(true);
      expect(marketplaceItems.updatedAt.notNull).toBe(true);
    });

    it('should have unique constraint on slug', () => {
      expect(marketplaceItems.slug.isUnique).toBe(true);
    });

    it('should have correct data types', () => {
      expect(marketplaceItems.id.dataType).toBe('string');
      expect(marketplaceItems.name.dataType).toBe('string');
      expect(marketplaceItems.description.dataType).toBe('string');
      expect(marketplaceItems.downloadCount.dataType).toBe('number');
      expect(marketplaceItems.viewCount.dataType).toBe('number');
      expect(marketplaceItems.isFeatured.dataType).toBe('boolean');
      expect(marketplaceItems.isActive.dataType).toBe('boolean');
      expect(marketplaceItems.createdAt.dataType).toBe('date');
      expect(marketplaceItems.updatedAt.dataType).toBe('date');
    });
  });

  it('should be exportable and usable', () => {
    // Test that the schema can be used in queries
    expect(() => {
      const tableName = marketplaceItems._.name;
      const columns = Object.keys(marketplaceItems._.columns);
      expect(tableName).toBe('marketplace_items');
      expect(columns.length).toBeGreaterThan(30); // Should have many columns
      expect(columns).toContain('id');
      expect(columns).toContain('developerId');
      expect(columns).toContain('name');
      expect(columns).toContain('slug');
      expect(columns).toContain('description');
    }).not.toThrow();
  });
});
