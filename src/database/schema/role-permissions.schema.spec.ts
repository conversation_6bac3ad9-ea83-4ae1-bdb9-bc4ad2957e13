import { rolePermissions } from './role-permissions.schema';

describe('Role Permissions Schema', () => {
  it('should be defined', () => {
    expect(rolePermissions).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(rolePermissions._.name).toBe('role_permissions');
  });

  it('should have all required columns', () => {
    const columns = Object.keys(rolePermissions._.columns);
    expect(columns).toContain('id');
    expect(columns).toContain('roleId');
    expect(columns).toContain('permissionId');
    expect(columns).toContain('createdAt');
  });

  it('should have id as primary key', () => {
    expect(rolePermissions.id.primary).toBe(true);
  });

  it('should have roleId as not null', () => {
    expect(rolePermissions.roleId.notNull).toBe(true);
  });

  it('should have permissionId as not null', () => {
    expect(rolePermissions.permissionId.notNull).toBe(true);
  });

  it('should have createdAt as not null', () => {
    expect(rolePermissions.createdAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(rolePermissions.id.dataType).toBe('uuid');
    expect(rolePermissions.roleId.dataType).toBe('uuid');
    expect(rolePermissions.permissionId.dataType).toBe('uuid');
    expect(rolePermissions.createdAt.dataType).toBe('timestamp');
  });
});
