import { rolePermissions } from './role-permissions.schema';

describe('Role Permissions Schema', () => {
  it('should be defined', () => {
    expect(rolePermissions).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(rolePermissions[Symbol.for('drizzle:Name')]).toBe('role_permissions');
  });

  it('should have all required columns', () => {
    expect(rolePermissions.id).toBeDefined();
    expect(rolePermissions.roleId).toBeDefined();
    expect(rolePermissions.permissionId).toBeDefined();
    expect(rolePermissions.createdAt).toBeDefined();
  });

  it('should have id as primary key', () => {
    expect(rolePermissions.id.primary).toBe(true);
  });

  it('should have roleId as not null', () => {
    expect(rolePermissions.roleId.notNull).toBe(true);
  });

  it('should have permissionId as not null', () => {
    expect(rolePermissions.permissionId.notNull).toBe(true);
  });

  it('should have createdAt as not null', () => {
    expect(rolePermissions.createdAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(rolePermissions.id.dataType).toBe('string');
    expect(rolePermissions.roleId.dataType).toBe('string');
    expect(rolePermissions.permissionId.dataType).toBe('string');
    expect(rolePermissions.createdAt.dataType).toBe('date');
  });
});
