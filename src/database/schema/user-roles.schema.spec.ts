import { userRoles } from './user-roles.schema';

describe('User Roles Schema', () => {
  it('should be defined', () => {
    expect(userRoles).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(userRoles[Symbol.for('drizzle:Name')]).toBe('user_roles');
  });

  it('should have all required columns', () => {
    expect(userRoles.id).toBeDefined();
    expect(userRoles.userId).toBeDefined();
    expect(userRoles.roleId).toBeDefined();
    expect(userRoles.createdAt).toBeDefined();
  });

  it('should have id as primary key', () => {
    expect(userRoles.id.primary).toBe(true);
  });

  it('should have userId as not null', () => {
    expect(userRoles.userId.notNull).toBe(true);
  });

  it('should have roleId as not null', () => {
    expect(userRoles.roleId.notNull).toBe(true);
  });

  it('should have createdAt as not null', () => {
    expect(userRoles.createdAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(userRoles.id.dataType).toBe('string');
    expect(userRoles.userId.dataType).toBe('string');
    expect(userRoles.roleId.dataType).toBe('string');
    expect(userRoles.createdAt.dataType).toBe('date');
  });
});
