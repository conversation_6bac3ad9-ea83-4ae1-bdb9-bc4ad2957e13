import { userRoles } from './user-roles.schema';

describe('User Roles Schema', () => {
  it('should be defined', () => {
    expect(userRoles).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(userRoles._.name).toBe('user_roles');
  });

  it('should have all required columns', () => {
    const columns = Object.keys(userRoles._.columns);
    expect(columns).toContain('id');
    expect(columns).toContain('userId');
    expect(columns).toContain('roleId');
    expect(columns).toContain('createdAt');
  });

  it('should have id as primary key', () => {
    expect(userRoles.id.primary).toBe(true);
  });

  it('should have userId as not null', () => {
    expect(userRoles.userId.notNull).toBe(true);
  });

  it('should have roleId as not null', () => {
    expect(userRoles.roleId.notNull).toBe(true);
  });

  it('should have createdAt as not null', () => {
    expect(userRoles.createdAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(userRoles.id.dataType).toBe('uuid');
    expect(userRoles.userId.dataType).toBe('uuid');
    expect(userRoles.roleId.dataType).toBe('uuid');
    expect(userRoles.createdAt.dataType).toBe('timestamp');
  });
});
