import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseModule } from './database.module';

// Mock postgres to avoid actual database connections during testing
jest.mock('postgres', () => {
    const mockClient = jest.fn(() => {
        const mockQuery = jest.fn().mockResolvedValue([{ test: 1 }]);
        return mockQuery;
    });
    return {
        __esModule: true,
        default: mockClient,
    };
});

// Mock drizzle-orm
jest.mock('drizzle-orm/postgres-js', () => ({
    drizzle: jest.fn().mockReturnValue({
        select: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    }),
}));

// Mock the schema
jest.mock('./schema/index.js', () => ({
    users: {},
    roles: {},
    permissions: {},
}));

describe('DatabaseModule', () => {
    let module: TestingModule;

    beforeEach(async () => {
        // Mock console methods to avoid noise in tests
        jest.spyOn(console, 'log').mockImplementation();
        jest.spyOn(console, 'error').mockImplementation();

        module = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({
                    isGlobal: true,
                }),
                DatabaseModule,
            ],
        }).compile();
    });

    afterEach(async () => {
        if (module) {
            await module.close();
        }
        jest.restoreAllMocks();
    });

    it('should be defined', () => {
        expect(module).toBeDefined();
    });

    it('should provide DB token', () => {
        const db = module.get('DB');
        expect(db).toBeDefined();
    });

    it('should export DB token', () => {
        const exports = Reflect.getMetadata('exports', DatabaseModule);
        expect(exports).toContain('DB');
    });

    describe('module metadata', () => {
        it('should have correct providers configuration', () => {
            const providers = Reflect.getMetadata('providers', DatabaseModule);
            expect(providers).toBeDefined();
            expect(Array.isArray(providers)).toBe(true);
            expect(providers).toHaveLength(1);
            expect(providers[0]).toHaveProperty('provide', 'DB');
            expect(providers[0]).toHaveProperty('useFactory');
            expect(providers[0]).toHaveProperty('inject');
        });

        it('should inject ConfigService into DB provider', () => {
            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];
            expect(dbProvider.inject).toContain(ConfigService);
        });
    });

    describe('environment-specific configuration', () => {
        it('should configure drizzle logger based on environment variables', () => {
            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(dbProvider.useFactory).toBeDefined();
            expect(typeof dbProvider.useFactory).toBe('function');
        });

        it('should handle DEBUG_SQL environment variable', () => {
            const originalDebugSql = process.env.DEBUG_SQL;
            const originalNodeEnv = process.env.NODE_ENV;

            // Test development with DEBUG_SQL=true
            process.env.NODE_ENV = 'development';
            process.env.DEBUG_SQL = 'true';

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(dbProvider.useFactory).toBeDefined();

            // Restore environment
            process.env.DEBUG_SQL = originalDebugSql;
            process.env.NODE_ENV = originalNodeEnv;
        });
    });
});